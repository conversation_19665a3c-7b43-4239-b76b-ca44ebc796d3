Respond.io API Guide for Deleting Tags:

Deletes tags for a specific contact.
DELETE
https://api.respond.io/v2/contact/{identifier}/tag


Request
Security: <PERSON><PERSON> Auth
Provide your bearer token in the Authorization header when making requests to protected resources.

Example: Authorization: Bearer 123


Path Parameters
identifier
string
required
Identifier of the contact that can either be a phone number, email or contact ID. Examples: id:123 , email:<EMAIL> , phone:+60121233112


Query Parameters
identify
string
Match pattern:
asd



Body

application/json

application/json
array[string]
Array of tags to be added to the contact

>= 1 items
<= 10 items





Auth
Token: 123

Parameters
identifier*: string
identify: string

Body
[
  "abc"
]