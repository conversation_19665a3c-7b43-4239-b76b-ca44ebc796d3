# Google Docs Credential Configuration for n8n AI Agent

This guide provides step-by-step instructions for setting up Google Docs as a direct tool/sub-node within n8n AI Agent for real-time knowledge base access.

## Overview

Using Google Docs as a direct tool in n8n AI Agent allows your AI to:
- Read Google Docs content in real-time during conversations
- Access up-to-date information without preprocessing
- Use documents as a knowledge base for customer support, FAQs, policies, etc.
- Automatically incorporate document content into AI responses

## Why OAuth is Required (Unfortunately, No Way to Skip)

**You're right - this seems overly complex for a simple automation!** However, Google requires OAuth for ALL programmatic access to Google Docs, even for reading your own documents. Here's why:

### Google's Security Model
- **No API Keys for Docs**: Unlike some Google services, Google Docs API doesn't support simple API key authentication
- **User Context Required**: Google needs to know WHICH user's documents to access
- **Scope-based Permissions**: OAuth allows fine-grained control over what the integration can access

### What You're Actually Creating
You're not creating a "public app" - you're creating:
- **Personal Integration**: Only for your own use
- **Private OAuth Client**: Not published or reviewed by Google
- **Internal Tool**: Just a way for n8n to authenticate as "you" to access your docs

### The "App" is Just Authentication
Think of it as creating a "key" that allows n8n to say "I'm acting on behalf of [your name] to read their Google Docs"

## Alternative Approaches (If You Want to Avoid OAuth)

### Option 1: Public Google Docs (Read-Only)
If your documents can be public, you can:
1. Set Google Doc sharing to "Anyone with the link can view"
2. Use n8n's **HTTP Request** node to fetch the document as plain text
3. URL format: `https://docs.google.com/document/d/[DOCUMENT_ID]/export?format=txt`

**Pros**: No OAuth needed, very simple
**Cons**: Document must be public, limited formatting, no real-time updates

### Option 2: Copy Content to Other Platforms
- **Notion**: Has simpler API authentication
- **Airtable**: Uses API keys instead of OAuth
- **Simple text files**: Store in GitHub, Dropbox, etc.
- **n8n Form**: Create a simple form to update content

### Option 3: Manual Content Updates
- Copy/paste content into n8n workflow variables
- Use n8n's built-in data storage
- Update content through n8n interface when needed

## If You Must Use Google Docs (OAuth Required)

Unfortunately, if you want to use Google Docs with proper authentication and private documents, OAuth is the ONLY way Google allows this. Here's why it's actually simpler than it looks:

### Simplified Understanding
1. **You're not building an app for others** - just for yourself
2. **One-time setup** - Configure once, use forever
3. **Google's requirement** - Not n8n's choice
4. **5-10 minutes total** - Faster than migrating content elsewhere

## Prerequisites

- Google Cloud Console account (free)
- n8n instance (Cloud or self-hosted)
- Google Docs document(s) to use as knowledge base

## Step 1: Create Google Cloud Console Project

1. **Log in to Google Cloud Console**
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Sign in with your Google account

2. **Create New Project**
   - Click the project dropdown in the top navigation
   - Select **"New Project"** or go to [New Project page](https://console.cloud.google.com/projectcreate)
   - Enter a **Project name** (e.g., "n8n-google-docs-integration")
   - Select the **Location** for your project
   - Click **"Create"**

3. **Verify Project Selection**
   - Check the project dropdown shows your new project
   - If not, select your newly created project

## Step 2: Enable Required APIs

1. **Navigate to APIs & Services**
   - Go to [Google Cloud Console - Library](https://console.cloud.google.com/apis/library)
   - Ensure you're in the correct project

2. **Enable Google Docs API**
   - Search for "Google Docs API"
   - Click on the result
   - Click **"ENABLE"**

3. **Enable Google Drive API** (Required)
   - Search for "Google Drive API"
   - Click on the result
   - Click **"ENABLE"**
   
   > **Note**: Google Drive API is required for Google Docs integration in n8n

## Step 3: Configure OAuth Consent Screen (Simplified for Personal Use)

> **Remember**: You're not creating a public app! This is just Google's way of letting n8n access YOUR documents.

1. **Access OAuth Consent Screen**
   - Go to **APIs & Services > OAuth consent screen**
   - Click **"Get started"** if first time

2. **Choose User Type** (Choose External - It's Simpler)
   - Select **"External"** (even for personal use)
   - This allows you to use it without Google Workspace
   - Click **"Create"**

3. **Configure App Information** (Minimal Required Fields)
   - **App name**: "My n8n Integration" (anything you want)
   - **User support email**: Your email address
   - **Developer contact information**: Your email address
   - **Leave everything else blank** - not needed for personal use

4. **Add Authorized Domains**
   - In **"Authorized domains"** section, click **"Add domain"**
   - For n8n Cloud: Add `n8n.cloud`
   - For self-hosted: Add your n8n instance domain (e.g., `yourdomain.com`)
   - Click **"Save and Continue"**

5. **Skip Scopes and Test Users**
   - Click **"Save and Continue"** through the remaining screens
   - You don't need to add scopes manually - n8n handles this
   - No test users needed for personal use

## Step 4: Create OAuth Client Credentials

1. **Navigate to Credentials**
   - Go to **APIs & Services > Credentials**
   - Click **"+ Create Credentials"**
   - Select **"OAuth client ID"**

2. **Configure OAuth Client**
   - **Application type**: Select **"Web application"**
   - **Name**: Enter descriptive name (e.g., "Personal n8n Integration" or "My Docs Reader")

   > **IMPORTANT**: Don't use "n8n" in the name! Google may flag it as impersonating n8n's official app.

3. **Set Authorized Redirect URIs**
   - **CRITICAL**: You must use YOUR OWN domain, not n8n.cloud's domain
   - For n8n Cloud users, this is the problem! You need to create your own OAuth app
   - **For n8n Cloud**: You'll need to use a different approach (see solutions below)
   - **For self-hosted n8n**: Use your domain:
     ```
     https://your-n8n-domain.com/rest/oauth2-credential/callback
     ```
   - Click **"Create"**

4. **Save Credentials**
   - Copy the **Client ID** (long string starting with numbers)
   - Copy the **Client Secret** (shorter string with letters/numbers)
   - Keep these secure - you'll need them for n8n

## Step 5: Configure n8n Credentials (After Fixing Verification)

1. **Create New Credential in n8n**
   - In n8n, go to **Settings > Credentials**
   - Click **"Add Credential"**
   - Search for and select **"Google OAuth2 API"** or **"Google"**

2. **Fill Credential Fields**
   ```
   OAuth Redirect URL: https://oauth.n8n.cloud/oauth2/callback
   Client ID: [Paste your Google Client ID]
   Client Secret: [Paste your Google Client Secret]
   ```

3. **Authenticate with Google**
   - Click **"Sign in with Google"**
   - Choose your Google account (the one you added as test user)
   - If you see the verification error, make sure you completed Solution 1 above
   - Grant permissions when prompted
   - You should see "Authentication successful"

4. **Save Credential**
   - Give your credential a descriptive name (e.g., "Google Docs Knowledge Base")
   - Click **"Save"**

## Alternative: Use Google's Pre-built n8n Integration (If Available)

Some users report that n8n Cloud has pre-configured Google integrations:

1. **Look for "Sign in with Google" option**
   - When creating Google credentials in n8n
   - Look for a blue "Sign in with Google" button
   - This uses n8n's pre-verified OAuth app

2. **If this option exists:**
   - You don't need to create your own OAuth app
   - Just click the button and authenticate
   - This bypasses the verification issue entirely

## Step 6: Set Up AI Agent with Google Docs Tool

1. **Create AI Agent Workflow**
   ```
   Chat Trigger → AI Agent → Google Docs Tool (sub-node)
                      ↓
                 Chat Model (OpenAI/Gemini)
   ```

2. **Configure Google Docs Tool**
   - Connect **Google Docs** node as sub-node to AI Agent
   - **Tool Name**: "Knowledge Base Reader"
   - **Tool Description**: "Use this tool to access company policies, FAQs, and documentation stored in Google Docs"
   - **Credentials**: Select your Google credential
   - **Operation**: "Get" (to read document content)

3. **Configure Document Access**
   - **Document ID**: Extract from Google Docs URL
     ```
     https://docs.google.com/document/d/[DOCUMENT_ID]/edit
     ```
   - Copy the long string between `/d/` and `/edit`

4. **Set AI Agent System Message**
   ```
   You are a helpful assistant with access to our knowledge base stored in Google Docs.

   When users ask questions:
   1. Use the Google Docs tool to retrieve relevant information
   2. Base your answers on the document content
   3. If information isn't in the docs, clearly state that
   4. Always provide accurate, up-to-date information

   Use the Knowledge Base Reader tool before answering any questions.
   ```

## Step 7: Test the Integration

1. **Test Document Access**
   - Execute the Google Docs node manually
   - Verify it returns document content
   - Check for any authentication errors

2. **Test AI Agent**
   - Use the Chat interface
   - Ask questions related to your document content
   - Verify the AI uses the Google Docs tool
   - Confirm responses include document information

## Troubleshooting

### Common Issues

## FIXING THE "Google Verification" ERROR

### The Problem You're Seeing
```
n8n.cloud has not completed the Google verification process.
The app is currently being tested and can only be accessed by developer-approved testers.
```

### Why This Happens
- You're trying to use n8n.cloud's redirect URL in YOUR OAuth app
- Google sees this as impersonating n8n's official integration
- Google requires verification for apps using external domains

### SOLUTIONS (Choose One):

#### Solution 1: Add Yourself as Test User (Easiest)
1. In Google Cloud Console, go to **OAuth consent screen**
2. Scroll down to **"Test users"** section
3. Click **"Add users"**
4. Add your own email address
5. Click **"Save"**
6. Now try the authentication again - it should work!

#### Solution 2: Use "Internal" User Type (If You Have Google Workspace)
1. Go back to **OAuth consent screen**
2. Change **User Type** from "External" to "Internal"
3. This bypasses verification for your organization
4. Only works if you have Google Workspace (not free Gmail)

#### Solution 3: Publish Your App (For Advanced Users)
1. In **OAuth consent screen**, click **"Publish App"**
2. This makes it available to anyone (not recommended for personal use)
3. May require Google review process

**"Google hasn't verified this app" Warning (Different Issue)**
- If you see this warning instead of the error above:
- **This is completely normal and safe for personal use!**
- Click "Advanced" → "Go to [app name] (unsafe)" to proceed
- The "unsafe" label is misleading - you created this yourself

**Authentication Errors**
- Verify OAuth redirect URL matches exactly
- Check that both Google Docs and Drive APIs are enabled
- Ensure credentials are saved correctly in n8n

**Document Access Issues**
- Verify document ID is correct
- Ensure the Google account has access to the document
- Check document sharing permissions

**Tool Not Working in AI Agent**
- Verify Google Docs tool is connected as sub-node
- Check tool name and description are clear
- Ensure system message instructs AI to use the tool

### Best Practices

1. **Document Organization**
   - Use clear headings and structure
   - Keep documents under 10,000 words for best performance
   - Include table of contents for longer documents

2. **Security**
   - Use least-privilege access for Google accounts
   - Regularly review OAuth app permissions
   - Monitor API usage in Google Cloud Console

3. **Performance**
   - Consider document size limits
   - Monitor API rate limits
   - Cache frequently accessed content if needed

## Next Steps

- Set up multiple Google Docs tools for different knowledge areas
- Implement fallback responses for missing information
- Monitor usage and optimize document structure
- Consider hybrid approaches with other data sources

## Support Resources

- [n8n Google Docs Documentation](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googledocs/)
- [Google Cloud OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
- [n8n AI Agent Documentation](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent/)
