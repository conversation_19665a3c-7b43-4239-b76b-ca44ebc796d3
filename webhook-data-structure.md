[{"headers": {"host": "invade.app.n8n.cloud", "user-agent": "axios/1.10.0", "content-length": "922", "accept": "application/json, text/plain, */*", "accept-encoding": "gzip, br", "baggage": "sentry-environment=production,sentry-public_key=f872ea482f6f4ba1bdf13e0cb58c273f,sentry-trace_id=944cf7d35f5940ca9e4da199ada08170", "cdn-loop": "cloudflare; loops=1; subreqs=1", "cf-connecting-ip": "*************", "cf-ew-via": "15", "cf-ipcountry": "SG", "cf-ray": "95c057fa77bdce4b-SIN", "cf-visitor": "{\"scheme\":\"https\"}", "cf-worker": "n8n.cloud", "content-type": "application/json", "sentry-trace": "944cf7d35f5940ca9e4da199ada08170-bfd84b999d138cee", "x-forwarded-for": "*************, *************", "x-forwarded-host": "invade.app.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "traefik-prod-users-gwc-14-5654dfbc89-phbvb", "x-is-trusted": "yes", "x-real-ip": "*************", "x-webhook-signature": "NvZiz0Mu226gLaa+Pco7riPeBGylT/MafnclBxtnOxM="}, "params": {}, "query": {}, "body": {"event_type": "message.received", "event_id": "b534693b-668f-4ee8-b8d8-a9f7e3f8b5c9", "contact": {"id": 295154053, "firstName": "<PERSON>", "lastName": "Callao", "phone": null, "email": null, "language": null, "profilePic": null, "countryCode": null, "status": "open", "assignee": {"id": 854657, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Callao"}, "created_at": 1751891401, "lifecycle": ""}, "message": {"messageId": 1751985694456000, "channelMessageId": "aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDU5NDAxNTM2MzQwOjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI3NjAxNzMxNjAxMDYyMjE5NzozMjMxODQzMTcyNjQzMzQzMzIwNzE4NzMzMTA1MDYzNTI2NAZDZD", "contactId": 295154053, "channelId": 395043, "traffic": "incoming", "timestamp": 1751985694456, "message": {"type": "text", "text": "agent"}}, "channel": {"id": 395043, "name": "Instagram", "source": "instagram", "meta": "{\"meta\":{\"name\":\"<PERSON>\",\"id\":\"1952074268895477\"}}", "created_at": 1751879505}}, "webhookUrl": "https://invade.app.n8n.cloud/webhook-test/b0e9c7c0-95ec-4d53-a949-db248ae0ada7", "executionMode": "test"}]