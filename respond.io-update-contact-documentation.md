Update a contact API Documentation

Update a contact
put
https://api.respond.io/v2/contact/{identifier}

Updates an existing contact. Only the contact fields that are included in the body will be updated. The first contact will be updated when duplicate contacts with the defined identifier exist.


Request
Security: Bear<PERSON> Auth
Provide your bearer token in the Authorization header when making requests to protected resources.

Example: Authorization: Bearer 123


Path Parameters
identifier
string
required
Identifier of the contact that can either be a phone number, email or contact ID. Examples: id:123 , email:<EMAIL> , phone:+60121233112



Body

application/json

firstName
string
required
Example:
Muhammad
lastName
string or null
Example:
Mahin
phone
string or null
Example:
+60123456789
email
string or null
Example:
<EMAIL>
language
string or null
Following the ISO 639-1 standard.

Example:
ms
profilePic
string or null
Example:
https://cdn.respond.io/profile_avatar.png
countryCode
string or null
Following the ISO 3166-1 alpha-2 standard.

Example:
MY
custom_fields
array[object] or null
name
string
required
Example:
Company Website
value
string or null
Formatting rulesShow all...

Example:
https://example.com





Auth
Token: 123

Parameters
identifier*: string

Body

{
  "firstName": "<PERSON> ",
  "lastName": "<PERSON>hin",
  "phone": "+60123456789",
  "email": "<EMAIL>",
  "language": "ms",
  "profilePic": "https://cdn.respond.io/profile_avatar.png",
  "countryCode": "MY",
  "custom_fields": [
    {
      "name": "Company Website",
      "value": "https://example.com"
    }
  ]
}