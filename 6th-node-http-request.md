A. PARAMETERS

1. Method: DELETE

2. URL: https://api.respond.io/v2/contact/id:{{ $json.body.contact.id }}/tag

3. Authentication (Choose below):
        ✓ Generic Credential Type
        Fully customizable. Choose between basic, header, OAuth2, etc.)

4. If chosen Generic Credential Type Above
Generic Auth Type (Select from below):
✓ Bearer Auth

5. Send Query Parameters (Check or Uncheck): ✗ Unchecked

6. Send Headers (Check or Uncheck): ✓ Checked

7. If checked Send Headers
Specify Headers (Select from below):
✓ Using Fields Below (If choose this, then what to put?):
Name: Content-Type
Value: application/json

8. Send Body (Check or Uncheck): ✓ Checked

9. If checked Send Body
Specify Body (Select from below):
✓ Using JSON  (If choose this then what to put?):
[
  "human_needed"
]

10. Options (Choose below if necessary. Type "not necessary" if not):
Response: ✓ Include Response Headers
All others: not necessary






B. SETTINGS

Check or Uncheck the following:
✓ SSL Certificates: Checked (for secure HTTPS communication)

✓ Always Output Data: Checked (to ensure data flows to next node)

✗ Execute Once: Unchecked (allow multiple executions)

✓ Retry On Fail: Checked (for API reliability)

On Error (If checked, choose below):
✓ Continue (using error output)
Pass item to an extra `error` output for error handling