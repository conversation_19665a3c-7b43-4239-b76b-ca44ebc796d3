Simplified n8n Flow - MVP Version
Updated n8n Flow - MVP Version with AI Agent & Telegram
🎯 Linear Flow Path (11 Nodes Total)
Step-by-Step Connection:
1. Start with Webhook Trigger
Receives incoming messages from Respond.io
Then connect to →
2. Get Contact Details (HTTP Request)
API: GET https://api.respond.io/v2/contact/id:{{ $json.body.contact.id }}
Retrieves current contact information including tags
Then connect to →
3. Tag Status Check (IF Node)
IF condition: Contact has "human_needed" tag
Expression: {{ $json.tags.includes('human_needed') }}
TRUE path: END FLOW (<PERSON><PERSON> stays silent - human intervention needed)
FALSE path: Connect to Step 4 (Continue Processing)
Then connect to →
4. Escalation Check (IF Node)
IF condition: Message contains keywords like "human", "manager", "agent", "staff"
TRUE path: Connect to Step 5 (Human Escalation)
FALSE path: Connect to Step 8 (AI Processing)
HUMAN PATH:
5. Tag for Human (HTTP Request)
Adds "human_needed" tag to contact in Respond.io
Then connect to →
6. Telegram Notification (Telegram Bot)
Sends notification to admin: "New escalation needed for {{$json.body.contact.firstName}}"
Includes contact details and original message
Then connect to →
7. Wait 12 Hours (Delay Node)
Pauses execution for 12 hours
Then connect to →
8. Remove Escalation Tag (HTTP Request)
Removes "human_needed" tag after 12 hours
Then END the flow
AI PATH:
7. AI Agent (with 3 sub-nodes)
Sub-node 1: Chat Model → Google Gemini AI
Sub-node 2: Memory → Simple Memory Node
Sub-node 3: Tool → Google Docs (pulls latest restaurant info)
AI Instructions: "You are helpful front desk staff at Tabu Bali restaurant"
Then connect to →
8. Store AI Response in Respond.io (HTTP Request)
Updates contact with AI response in custom field so Respond.io can access it
API: PUT https://api.respond.io/v2/contact/id:{{ $json.body.contact.id }}
Body: {
  "custom_fields": [
    {
      "name": "ai_response",
      "value": "{{ $json.output }}"
    }
  ]
}
Then END the flow

SETUP COMPLETED IN RESPOND.IO:
✓ Custom field "ai_response" already created manually in Respond.io
✓ Field configured to receive AI Agent output via {{ $json.output }}
✓ Respond.io workflow can use "Contact Field Updated" trigger for "ai_response"
✓ Message template can access via {{custom_fields.ai_response}}

RESULT:
- Full conversation tracking maintained in Respond.io inbox
- AI responses automatically sent via Respond.io's system (not direct API)
- Agents can see complete conversation history and analytics
- All data properly tracked for reporting and management
