Respond.io API Guide for Adding Tags:

Add tags
post
https://api.respond.io/v2/contact/{identifier}/tag
Adds tags for a specific contact.


Request
Security: Bear<PERSON> Auth
Provide your bearer token in the Authorization header when making requests to protected resources.

Example: Authorization: Bearer 123


Path Parameters
identifier
string
required
Identifier of the contact that can either be a phone number, email or contact ID. Examples: id:123 , email:<EMAIL> , phone:+60121233112



Body

application/json

application/json
array[string]
Array of tags to be added to the contact

>= 1 items
<= 10 items





Auth
Token: 123

Parameters
identifier: string

Body
[
  "abc"
]